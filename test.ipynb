{"cells": [{"cell_type": "code", "execution_count": 11, "id": "84146dc0", "metadata": {}, "outputs": [], "source": ["import google.generativeai as genai"]}, {"cell_type": "code", "execution_count": 12, "id": "1a2856fd", "metadata": {}, "outputs": [], "source": ["genai.configure(api_key=\"AIzaSyBuzZKNMW-Cp3VjCeMebVZIU0FkMvtjEWE\")"]}, {"cell_type": "code", "execution_count": 13, "id": "dd390a7a", "metadata": {}, "outputs": [], "source": ["# List of word items, each with the word text and its timestamp (if available)\n", "response_schema = {\n", "    \"type\": \"OBJECT\",\n", "    \"properties\": {\n", "        \"transcript\": {\n", "            \"type\": \"ARRAY\",\n", "            \"items\": {\n", "                \"type\": \"OBJECT\",\n", "                \"properties\": {\n", "                    \"word\": {\"type\": \"STRING\"},\n", "                    \"start_time\": {\"type\": \"STRING\"},  # Model-generated timestamps as \"00:00:05.230\"\n", "                    \"end_time\": {\"type\": \"STRING\"}\n", "                },\n", "                \"required\": [\"word\"]\n", "            }\n", "        }\n", "    },\n", "    \"required\": [\"transcript\"]\n", "}\n"]}, {"cell_type": "code", "execution_count": 14, "id": "647ee666", "metadata": {}, "outputs": [], "source": ["import google.genai as genai\n", "\n", "# Initialize client with your Gemini API key\n", "client = genai.Client(api_key=\"AIzaSyBuzZKNMW-Cp3VjCeMebVZIU0FkMvtjEWE\")"]}, {"cell_type": "code", "execution_count": 18, "id": "e12291eb", "metadata": {}, "outputs": [], "source": ["audio_file = client.files.upload(file=\"/Users/<USER>/Voice_Process/1746690554.133380 (2).wav\")\n"]}, {"cell_type": "code", "execution_count": 20, "id": "28829517", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"transcript\": [{\"word\": \"हजुर\", \"start_time\": \"00:00:01\", \"end_time\": \"00:00:01\"}, {\"word\": \"सर\", \"start_time\": \"00:00:01\", \"end_time\": \"00:00:01\"}, {\"word\": \"हजुर\", \"start_time\": \"00:00:02\", \"end_time\": \"00:00:02\"}, {\"word\": \"नमस्कार\", \"start_time\": \"00:00:02\", \"end_time\": \"00:00:02\"}, {\"word\": \"सर\", \"start_time\": \"00:00:02\", \"end_time\": \"00:00:03\"}, {\"word\": \"नमस्कार\", \"start_time\": \"00:00:03\", \"end_time\": \"00:00:04\"}, {\"word\": \"सर\", \"start_time\": \"00:00:05\", \"end_time\": \"00:00:05\"}, {\"word\": \"धनबहादुर\", \"start_time\": \"00:00:05\", \"end_time\": \"00:00:06\"}, {\"word\": \"जी\", \"start_time\": \"00:00:06\", \"end_time\": \"00:00:06\"}, {\"word\": \"बोल्दै\", \"start_time\": \"00:00:06\", \"end_time\": \"00:00:06\"}, {\"word\": \"हुनुहुन्छ\", \"start_time\": \"00:00:06\", \"end_time\": \"00:00:07\"}, {\"word\": \"हजुर\", \"start_time\": \"00:00:07\", \"end_time\": \"00:00:08\"}, {\"word\": \"हजुरले\", \"start_time\": \"00:00:08\", \"end_time\": \"00:00:08\"}, {\"word\": \"चाहिँ\", \"start_time\": \"00:00:08\", \"end_time\": \"00:00:09\"}, {\"word\": \"यो\", \"start_time\": \"00:00:09\", \"end_time\": \"00:00:09\"}, {\"word\": \"बुकको\", \"start_time\": \"00:00:09\", \"end_time\": \"00:00:09\"}, {\"word\": \"लागि\", \"start_time\": \"00:00:10\", \"end_time\": \"00:00:10\"}, {\"word\": \"फेरि\", \"start_time\": \"00:00:10\", \"end_time\": \"00:00:10\"}, {\"word\": \"कल\", \"start_time\": \"00:00:10\", \"end_time\": \"00:00:11\"}, {\"word\": \"गर्नु\", \"start_time\": \"00:00:11\", \"end_time\": \"00:00:11\"}, {\"word\": \"भन्नुभएको\", \"start_time\": \"00:00:11\", \"end_time\": \"00:00:11\"}, {\"word\": \"रहेछ\", \"start_time\": \"00:00:11\", \"end_time\": \"00:00:11\"}, {\"word\": \"नि\", \"start_time\": \"00:00:11\", \"end_time\": \"00:00:12\"}, {\"word\": \"सर\", \"start_time\": \"00:00:12\", \"end_time\": \"00:00:12\"}, {\"word\": \"हजुर\", \"start_time\": \"00:00:13\", \"end_time\": \"00:00:14\"}, {\"word\": \"कति\", \"start_time\": \"00:00:14\", \"end_time\": \"00:00:14\"}, {\"word\": \"बेला\", \"start_time\": \"00:00:14\", \"end_time\": \"00:00:15\"}, {\"word\": \"हो\", \"start_time\": \"00:00:15\", \"end_time\": \"00:00:15\"}, {\"word\": \"र\", \"start_time\": \"00:00:15\", \"end_time\": \"00:00:15\"}, {\"word\": \"ए\", \"start_time\": \"00:00:16\", \"end_time\": \"00:00:16\"}, {\"word\": \"होइन\", \"start_time\": \"00:00:16\", \"end_time\": \"00:00:16\"}, {\"word\": \"हजुरले\", \"start_time\": \"00:00:16\", \"end_time\": \"00:00:17\"}, {\"word\": \"नम्बर\", \"start_time\": \"00:00:17\", \"end_time\": \"00:00:17\"}, {\"word\": \"दिनुभएको\", \"start_time\": \"00:00:17\", \"end_time\": \"00:00:17\"}, {\"word\": \"थिएन\", \"start_time\": \"00:00:17\", \"end_time\": \"00:00:18\"}, {\"word\": \"सर\", \"start_time\": \"00:00:18\", \"end_time\": \"00:00:18\"}, {\"word\": \"हजुर\", \"start_time\": \"00:00:20\", \"end_time\": \"00:00:20\"}, {\"word\": \"मैले\", \"start_time\": \"00:00:20\", \"end_time\": \"00:00:20\"}, {\"word\": \"त\", \"start_time\": \"00:00:20\", \"end_time\": \"00:00:20\"}, {\"word\": \"अर्डर\", \"start_time\": \"00:00:20\", \"end_time\": \"00:00:21\"}, {\"word\": \"गरिसके\", \"start_time\": \"00:00:21\", \"end_time\": \"00:00:21\"}, {\"word\": \"अहिले\", \"start_time\": \"00:00:21\", \"end_time\": \"00:00:22\"}, {\"word\": \"३-४\", \"start_time\": \"00:00:22\", \"end_time\": \"00:00:22\"}, {\"word\": \"बजे\", \"start_time\": \"00:00:22\", \"end_time\": \"00:00:22\"}, {\"word\": \"तिर\", \"start_time\": \"00:00:22\", \"end_time\": \"00:00:23\"}, {\"word\": \"ल्याइदिन्छु\", \"start_time\": \"00:00:23\", \"end_time\": \"00:00:23\"}, {\"word\": \"भन्नुभएको\", \"start_time\": \"00:00:23\", \"end_time\": \"00:00:24\"}, {\"word\": \"थियो\", \"start_time\": \"00:00:24\", \"end_time\": \"00:00:24\"}, {\"word\": \"ए\", \"start_time\": \"00:00:24\", \"end_time\": \"00:00:25\"}, {\"word\": \"त्यसको\", \"start_time\": \"00:00:25\", \"end_time\": \"00:00:25\"}, {\"word\": \"लागि\", \"start_time\": \"00:00:25\", \"end_time\": \"00:00:25\"}, {\"word\": \"कल\", \"start_time\": \"00:00:25\", \"end_time\": \"00:00:25\"}, {\"word\": \"आएको\", \"start_time\": \"00:00:26\", \"end_time\": \"00:00:26\"}, {\"word\": \"थियो\", \"start_time\": \"00:00:26\", \"end_time\": \"00:00:26\"}, {\"word\": \"अघि\", \"start_time\": \"00:00:27\", \"end_time\": \"00:00:27\"}, {\"word\": \"नै\", \"start_time\": \"00:00:27\", \"end_time\": \"00:00:28\"}, {\"word\": \"आएको\", \"start_time\": \"00:00:28\", \"end_time\": \"00:00:28\"}, {\"word\": \"थियो\", \"start_time\": \"00:00:28\", \"end_time\": \"00:00:29\"}, {\"word\": \"ए\", \"start_time\": \"00:00:29\", \"end_time\": \"00:00:29\"}, {\"word\": \"ठिकै\", \"start_time\": \"00:00:29\", \"end_time\": \"00:00:30\"}, {\"word\": \"छ\", \"start_time\": \"00:00:30\", \"end_time\": \"00:00:30\"}, {\"word\": \"सर\", \"start_time\": \"00:00:30\", \"end_time\": \"00:00:30\"}, {\"word\": \"मैले\", \"start_time\": \"00:00:30\", \"end_time\": \"00:00:30\"}, {\"word\": \"त्यही\", \"start_time\": \"00:00:30\", \"end_time\": \"00:00:31\"}, {\"word\": \"बुझ्नलाई\", \"start_time\": \"00:00:31\", \"end_time\": \"00:00:31\"}, {\"word\": \"कल\", \"start_time\": \"00:00:31\", \"end_time\": \"00:00:31\"}, {\"word\": \"गरेको\", \"start_time\": \"00:00:31\", \"end_time\": \"00:00:31\"}, {\"word\": \"थिएँ\", \"start_time\": \"00:00:31\", \"end_time\": \"00:00:32\"}, {\"word\": \"यो\", \"start_time\": \"00:00:32\", \"end_time\": \"00:00:32\"}, {\"word\": \"हजुरलाई\", \"start_time\": \"00:00:32\", \"end_time\": \"00:00:32\"}, {\"word\": \"कल\", \"start_time\": \"00:00:32\", \"end_time\": \"00:00:32\"}, {\"word\": \"आयो\", \"start_time\": \"00:00:32\", \"end_time\": \"00:00:32\"}, {\"word\": \"कि\", \"start_time\": \"00:00:32\", \"end_time\": \"00:00:33\"}, {\"word\": \"आएन\", \"start_time\": \"00:00:33\", \"end_time\": \"00:00:33\"}, {\"word\": \"भन्न\", \"start_time\": \"00:00:33\", \"end_time\": \"00:00:33\"}, {\"word\": \"नि\", \"start_time\": \"00:00:33\", \"end_time\": \"00:00:33\"}, {\"word\": \"ए\", \"start_time\": \"00:00:33\", \"end_time\": \"00:00:34\"}, {\"word\": \"हुन्छ\", \"start_time\": \"00:00:34\", \"end_time\": \"00:00:34\"}, {\"word\": \"हुन्छ\", \"start_time\": \"00:00:34\", \"end_time\": \"00:00:34\"}, {\"word\": \"सर\", \"start_time\": \"00:00:34\", \"end_time\": \"00:00:35\"}, {\"word\": \"हस\", \"start_time\": \"00:00:35\", \"end_time\": \"00:00:35\"}, {\"word\": \"हस\", \"start_time\": \"00:00:35\", \"end_time\": \"00:00:36\"}, {\"word\": \"त\", \"start_time\": \"00:00:36\", \"end_time\": \"00:00:36\"}, {\"word\": \"सर\", \"start_time\": \"00:00:36\", \"end_time\": \"00:00:36\"}, {\"word\": \"हस\", \"start_time\": \"00:00:37\", \"end_time\": \"00:00:37\"}]}\n", "{'transcript': [{'word': 'हजुर', 'start_time': '00:00:01', 'end_time': '00:00:01'}, {'word': 'सर', 'start_time': '00:00:01', 'end_time': '00:00:01'}, {'word': 'हजुर', 'start_time': '00:00:02', 'end_time': '00:00:02'}, {'word': 'नमस्कार', 'start_time': '00:00:02', 'end_time': '00:00:02'}, {'word': 'सर', 'start_time': '00:00:02', 'end_time': '00:00:03'}, {'word': 'नमस्कार', 'start_time': '00:00:03', 'end_time': '00:00:04'}, {'word': 'सर', 'start_time': '00:00:05', 'end_time': '00:00:05'}, {'word': 'धनबहादुर', 'start_time': '00:00:05', 'end_time': '00:00:06'}, {'word': 'जी', 'start_time': '00:00:06', 'end_time': '00:00:06'}, {'word': 'बोल्दै', 'start_time': '00:00:06', 'end_time': '00:00:06'}, {'word': 'हुनुहुन्छ', 'start_time': '00:00:06', 'end_time': '00:00:07'}, {'word': 'हजुर', 'start_time': '00:00:07', 'end_time': '00:00:08'}, {'word': 'हजुरले', 'start_time': '00:00:08', 'end_time': '00:00:08'}, {'word': 'चाहिँ', 'start_time': '00:00:08', 'end_time': '00:00:09'}, {'word': 'यो', 'start_time': '00:00:09', 'end_time': '00:00:09'}, {'word': 'बुकको', 'start_time': '00:00:09', 'end_time': '00:00:09'}, {'word': 'लागि', 'start_time': '00:00:10', 'end_time': '00:00:10'}, {'word': 'फेरि', 'start_time': '00:00:10', 'end_time': '00:00:10'}, {'word': 'कल', 'start_time': '00:00:10', 'end_time': '00:00:11'}, {'word': 'गर्नु', 'start_time': '00:00:11', 'end_time': '00:00:11'}, {'word': 'भन्नुभएको', 'start_time': '00:00:11', 'end_time': '00:00:11'}, {'word': 'रहेछ', 'start_time': '00:00:11', 'end_time': '00:00:11'}, {'word': 'नि', 'start_time': '00:00:11', 'end_time': '00:00:12'}, {'word': 'सर', 'start_time': '00:00:12', 'end_time': '00:00:12'}, {'word': 'हजुर', 'start_time': '00:00:13', 'end_time': '00:00:14'}, {'word': 'कति', 'start_time': '00:00:14', 'end_time': '00:00:14'}, {'word': 'बेला', 'start_time': '00:00:14', 'end_time': '00:00:15'}, {'word': 'हो', 'start_time': '00:00:15', 'end_time': '00:00:15'}, {'word': 'र', 'start_time': '00:00:15', 'end_time': '00:00:15'}, {'word': 'ए', 'start_time': '00:00:16', 'end_time': '00:00:16'}, {'word': 'होइन', 'start_time': '00:00:16', 'end_time': '00:00:16'}, {'word': 'हजुरले', 'start_time': '00:00:16', 'end_time': '00:00:17'}, {'word': 'नम्बर', 'start_time': '00:00:17', 'end_time': '00:00:17'}, {'word': 'दिनुभएको', 'start_time': '00:00:17', 'end_time': '00:00:17'}, {'word': 'थिएन', 'start_time': '00:00:17', 'end_time': '00:00:18'}, {'word': 'सर', 'start_time': '00:00:18', 'end_time': '00:00:18'}, {'word': 'हजुर', 'start_time': '00:00:20', 'end_time': '00:00:20'}, {'word': 'मैले', 'start_time': '00:00:20', 'end_time': '00:00:20'}, {'word': 'त', 'start_time': '00:00:20', 'end_time': '00:00:20'}, {'word': 'अर्डर', 'start_time': '00:00:20', 'end_time': '00:00:21'}, {'word': 'गरिसके', 'start_time': '00:00:21', 'end_time': '00:00:21'}, {'word': 'अहिले', 'start_time': '00:00:21', 'end_time': '00:00:22'}, {'word': '३-४', 'start_time': '00:00:22', 'end_time': '00:00:22'}, {'word': 'बजे', 'start_time': '00:00:22', 'end_time': '00:00:22'}, {'word': 'तिर', 'start_time': '00:00:22', 'end_time': '00:00:23'}, {'word': 'ल्याइदिन्छु', 'start_time': '00:00:23', 'end_time': '00:00:23'}, {'word': 'भन्नुभएको', 'start_time': '00:00:23', 'end_time': '00:00:24'}, {'word': 'थियो', 'start_time': '00:00:24', 'end_time': '00:00:24'}, {'word': 'ए', 'start_time': '00:00:24', 'end_time': '00:00:25'}, {'word': 'त्यसको', 'start_time': '00:00:25', 'end_time': '00:00:25'}, {'word': 'लागि', 'start_time': '00:00:25', 'end_time': '00:00:25'}, {'word': 'कल', 'start_time': '00:00:25', 'end_time': '00:00:25'}, {'word': 'आएको', 'start_time': '00:00:26', 'end_time': '00:00:26'}, {'word': 'थियो', 'start_time': '00:00:26', 'end_time': '00:00:26'}, {'word': 'अघि', 'start_time': '00:00:27', 'end_time': '00:00:27'}, {'word': 'नै', 'start_time': '00:00:27', 'end_time': '00:00:28'}, {'word': 'आएको', 'start_time': '00:00:28', 'end_time': '00:00:28'}, {'word': 'थियो', 'start_time': '00:00:28', 'end_time': '00:00:29'}, {'word': 'ए', 'start_time': '00:00:29', 'end_time': '00:00:29'}, {'word': 'ठिकै', 'start_time': '00:00:29', 'end_time': '00:00:30'}, {'word': 'छ', 'start_time': '00:00:30', 'end_time': '00:00:30'}, {'word': 'सर', 'start_time': '00:00:30', 'end_time': '00:00:30'}, {'word': 'मैले', 'start_time': '00:00:30', 'end_time': '00:00:30'}, {'word': 'त्यही', 'start_time': '00:00:30', 'end_time': '00:00:31'}, {'word': 'बुझ्नलाई', 'start_time': '00:00:31', 'end_time': '00:00:31'}, {'word': 'कल', 'start_time': '00:00:31', 'end_time': '00:00:31'}, {'word': 'गरेको', 'start_time': '00:00:31', 'end_time': '00:00:31'}, {'word': 'थिएँ', 'start_time': '00:00:31', 'end_time': '00:00:32'}, {'word': 'यो', 'start_time': '00:00:32', 'end_time': '00:00:32'}, {'word': 'हजुरलाई', 'start_time': '00:00:32', 'end_time': '00:00:32'}, {'word': 'कल', 'start_time': '00:00:32', 'end_time': '00:00:32'}, {'word': 'आयो', 'start_time': '00:00:32', 'end_time': '00:00:32'}, {'word': 'कि', 'start_time': '00:00:32', 'end_time': '00:00:33'}, {'word': 'आएन', 'start_time': '00:00:33', 'end_time': '00:00:33'}, {'word': 'भन्न', 'start_time': '00:00:33', 'end_time': '00:00:33'}, {'word': 'नि', 'start_time': '00:00:33', 'end_time': '00:00:33'}, {'word': 'ए', 'start_time': '00:00:33', 'end_time': '00:00:34'}, {'word': 'हुन्छ', 'start_time': '00:00:34', 'end_time': '00:00:34'}, {'word': 'हुन्छ', 'start_time': '00:00:34', 'end_time': '00:00:34'}, {'word': 'सर', 'start_time': '00:00:34', 'end_time': '00:00:35'}, {'word': 'हस', 'start_time': '00:00:35', 'end_time': '00:00:35'}, {'word': 'हस', 'start_time': '00:00:35', 'end_time': '00:00:36'}, {'word': 'त', 'start_time': '00:00:36', 'end_time': '00:00:36'}, {'word': 'सर', 'start_time': '00:00:36', 'end_time': '00:00:36'}, {'word': 'हस', 'start_time': '00:00:37', 'end_time': '00:00:37'}]}\n"]}], "source": ["response = client.models.generate_content(\n", "    model=\"gemini-2.5-flash\",  # or the available multimodal model\n", "    contents=[\n", "        audio_file,\n", "        \"Transcribe this audio into a word-level JSON transcript with start_time and end_time.\"\n", "    ],\n", "    config={\n", "        \"response_mime_type\": \"application/json\",\n", "        \"response_schema\": response_schema,\n", "    }\n", ")\n", "\n", "print(response.text)    # JSON output\n", "print(response.parsed)  # Parsed Python object if availableb\n"]}, {"cell_type": "code", "execution_count": null, "id": "ba89eef7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8553a48c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d3aa3bac", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}